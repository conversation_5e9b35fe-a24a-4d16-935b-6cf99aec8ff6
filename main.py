import asyncio
import os
from dotenv import load_dotenv
from langchain_openai import ChatOpenAI
from mcp_use import MCPAgent, MCPClient


async def main():
    """Run the example using a configuration file."""
    # Load environment variables
    load_dotenv()

    config = {
        "mcpServers": {
            "fetch": {
                "type": "sse",
                "url": "https://mcp.api-inference.modelscope.net/da81fcffd39044/sse"
            }
        }
    }

    # Create MCPClient from config file
    client = MCPClient.from_dict(config)

    # Create LLM with OpenRouter configuration
    llm = ChatOpenAI(
        model="qwen/qwen3-32b",
        api_key=os.getenv("OPENROUTER_API_KEY"),  # Your OpenRouter API key
        base_url="https://openrouter.ai/api/v1",  # OpenRouter base URL
        max_tokens=4096,  # 增加输出token数量
        temperature=0.7,  # 控制创造性
        max_retries=3,  # 重试次数
        request_timeout=60,  # 请求超时时间
        model_kwargs={
            "max_context_length": 131072,  # Qwen3-32B 支持 128K 上下文
            "top_p": 0.9,  # 核采样参数
            "frequency_penalty": 0.1,  # 频率惩罚
            "presence_penalty": 0.1,  # 存在惩罚
            "stream": True,  # 启用流式输出
        }
    )

    # Create agent with the client
    agent = MCPAgent(llm=llm, client=client, max_steps=30)

    # Run the query
    result = await agent.run(
        "fetch page https://www.ruanyifeng.com/blog/",
        max_steps=30,
    )
    print(f"\nResult: {result}")

if __name__ == "__main__":
    # Run the appropriate example
    asyncio.run(main())
