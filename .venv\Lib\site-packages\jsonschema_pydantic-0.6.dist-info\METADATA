Metadata-Version: 2.1
Name: jsonschema_pydantic
Version: 0.6
Summary: Convert JSON Schemas to Pydantic models
Home-page: https://github.com/kreneskyp/jsonschema-pydantic/
Author: kreneskyp
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: pydantic >=1.10.0
Provides-Extra: test
Requires-Dist: pytest ; extra == 'test'
Requires-Dist: coverage ; extra == 'test'
Requires-Dist: flake8 ; extra == 'test'
Requires-Dist: black ; extra == 'test'
Requires-Dist: isort ; extra == 'test'
Requires-Dist: pytest-cov ; extra == 'test'
Requires-Dist: gitchangelog ; extra == 'test'
Requires-Dist: mkdocs ; extra == 'test'
Requires-Dist: pydantic <3.0.0,>=2.5.3 ; extra == 'test'

# jsonschema-pydantic

Simple transform of jsonschema to pydantic models.

## Supported jsonschema features

- primitive types
- objects
- arrays
- nested objects
- optional fields
- default values

## Install 

```
pip install jsonschema-pydantic
```

## Usage

```
from jsonschema_pydantic import jsonschema_to_pydantic

jsonschema = {
    "type": "object",
    "properties": {
        "name": {"type": "string"},
        "age": {"type": "integer"},
    },
    "required": ["name"],
}

pydantic_model = jsonschema_to_pydantic(jsonschema)
```

## Development

Run pytest test suite:

```
make test
```

### Linting

Run all linters

```
make lint
```

### Formatting

Format python code:

```
make fmt
```

### Documentation

Generate documentation:

```
make docs
```

### Contributions

Read the [CONTRIBUTING.md](CONTRIBUTING.md) file.
